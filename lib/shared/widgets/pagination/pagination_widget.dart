import 'package:flutter/cupertino.dart';


class PaginationWidget extends StatelessWidget {
  const PaginationWidget({
    super.key,
    required this.onPagination,
    required this.isPaginating,
    required this.child,
    required this.next,
  });

  final bool Function(ScrollEndNotification) onPagination;
  final bool isPaginating;
  final Widget child;
  final bool next;

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollEndNotification>(
      onNotification: (notification) {
        if (
            next &&
            notification.metrics.pixels ==
                notification.metrics.maxScrollExtent) {
          return onPagination.call(notification);
        } else {
          return false;
        }
      },
      child: Column(
        children: [
          Expanded(child: child),
          if (isPaginating)
            const Padding(
              padding: EdgeInsets.all(8),
              child: CupertinoActivityIndicator(
              ),
            )
        ],
      ),
    );
  }
}
