import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/widgets/attribution_widget.dart';
import 'package:pixs/shared/widgets/pagination/pagination_widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: AnimationLimiter(
          child: BlocBuilder<HomeCubit, HomeState>(
            builder: (context, state) {
              return PaginationWidget(
                isPaginating:
                    state.imageListFetchStatus == ApiFetchStatus.loading,
                next: (state.images?.length ?? 0) <= 2000,
                onPagination: (notification) {
                  if (state.images?.length == 2000) {
                    return false;
                  }
                  context.read<HomeCubit>().getImages(
                        page: (state.images?.length ?? 0) + 1,
                        isLoadMore: true,
                      );

                  return true;
                },
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: 8.0,
                    crossAxisSpacing: 8.0,
                  ),
                  itemCount: state.images?.length ?? 0,
                  itemBuilder: (BuildContext context, int index) =>
                      AnimationConfiguration.staggeredGrid(
                    position: index,
                    duration: const Duration(milliseconds: 500),
                    columnCount: 2,
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: ScaleAnimation(
                          scale: 0.9,
                          child: Hero(
                            tag: 'image_${state.images?[index].id}',
                            child: GestureDetector(
                              onTap: () => Navigator.pushNamed(
                                context,
                                routeImageDetail,
                                arguments: {'image': state.images?[index]},
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15.r),
                                  color: Colors.black,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 5,
                                      offset: const Offset(0, 3),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(15.r),
                                  child: Stack(
                                    children: [
                                      CachedNetworkImage(
                                        cacheKey: state.images?[index].urls?.small,
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                        imageUrl:
                                            state.images?[index].urls?.small ?? '',
                                        progressIndicatorBuilder:
                                            (context, url, progress) => Opacity(
                                          opacity: 0.5,
                                          child: Center(
                                            child: BlurHash(
                                              hash: state.images?[index].blurHash ?? '',
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) => Center(
                                          child: Opacity(
                                            opacity: 0.5,
                                            child: SizedBox(
                                              width: 50,
                                              child: Center(
                                                child: Image.asset(
                                                  Assets.kLogo,
                                                  width: 50,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // Compact attribution at bottom
                                      if (state.images?[index] != null)
                                        Positioned(
                                          bottom: 8.h,
                                          left: 8.w,
                                          right: 8.w,
                                          child: CompactAttributionWidget(
                                            image: state.images![index],
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
